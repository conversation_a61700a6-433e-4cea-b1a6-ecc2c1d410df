"""
Main Agent V2 - Modern LangChain implementation using LangGraph
Uses LangGraph's built-in persistence and memory management patterns
"""

import os
import logging
from datetime import datetime

from dotenv import load_dotenv

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate

from langgraph.checkpoint.mongodb import MongoDBSaver

from core.database import get_db_from_tenant_id
from utils import log_user_input, log_agent_response, log_chat_exchange, log_user_message, log_final_response, log_tool_call_clean
from utils.production_memory_manager import get_production_memory_manager

# Load environment variables
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)

# Setup main LLM
main_llm = ChatGoogleGenerativeAI(
    model="gemini-2.0-flash",
    temperature=0.1,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)


MAIN_AGENT_PROMPT = """You are a friendly, conversational sales representative for Ambition Guru Education. You help customers naturally and only use tools when specifically needed.

🚨 CRITICAL: DO NOT use tools for simple greetings, small talk, or general conversation. Respond naturally first.

🗣️ RESPOND NATURALLY WITHOUT TOOLS FOR:
- Greetings: "Hi", "Hello", "Hey", "Good morning"
- Small talk: "How are you?", "What's up?", "Nice to meet you"
- General conversation: "Thank you", "Okay", "I see"
- Follow-up questions about what you just said

📋 ONLY USE TOOLS WHEN USER SPECIFICALLY:
1. **search_products** - Asks "what courses do you have?", "show me courses", "what programs are available?"
2. **search_information** - Asks for help, company info, policies, or troubleshooting
3. **handle_booking** - Says "I want to book", "enroll me", "register for course"

🗣️ NATURAL CONVERSATION EXAMPLES:

User: "Hi"
You: "Hi there! Welcome to Ambition Guru Education. How can I help you today?"
[NO TOOLS NEEDED]

User: "Hello"
You: "Hello! I'm here to help you with any questions about our educational programs. What brings you here today?"
[NO TOOLS NEEDED]

User: "How are you?"
You: "I'm doing great, thank you for asking! I'm here to help you find the perfect course for your goals. What are you interested in learning?"
[NO TOOLS NEEDED]

User: "What courses do you have?"
You: "Let me show you our available courses!" [THEN USE search_products]

User: "I want to book a course"
You: "I'd be happy to help you with that!" [THEN USE handle_booking]

🎨 RESPONSE STYLE:
- Be warm, friendly, and conversational
- Build rapport naturally
- Ask follow-up questions to understand needs
- Only suggest tools/courses when user shows interest
- Use natural language, not robotic responses

PERSONALIZATION CONTEXT:
{personalized_context}

Remember: You're a human sales rep having a natural conversation. Don't jump to tools unless the user specifically needs information or wants to take action."""


class MainAgentV2:
    """
    Main Agent V2 - Clean implementation that works with ChatService
    Tools are provided by ChatService with current user context
    """

    def __init__(self, current_user=None):
        """Initialize the agent with current user context"""
        self.current_user = current_user

        # Use MongoDB checkpointer for persistent memory instead of in-memory MemorySaver
        if current_user and current_user.tenant_id:
            # Get tenant-specific database for memory persistence
            tenant_db = get_db_from_tenant_id(current_user.tenant_id)

            # Create MongoDB client from the database connection
            mongo_client = tenant_db.client

            # Use official MongoDB checkpointer
            self.memory = MongoDBSaver(
                client=mongo_client,
                db_name=tenant_db.name,
                collection_name=f"conversation_checkpoints_{current_user.tenant_id}"
            )

            # Initialize production memory system for persistent user sessions and profiles
            self.production_memory = get_production_memory_manager(current_user.tenant_id, "openai")

            logger.info(f"✅ MongoDB memory and memory system initialized for tenant: {current_user.tenant_id}")
        else:
            # Fallback to in-memory for cases without user context
            raise Exception("Tenant ID is required for MongoDB memory")

        self.llm = main_llm
        self.tools = []  # Tools will be set by ChatService
        self.agent = None  # Agent will be created when tools are set

        logger.info("✅ Main Agent V2 initialized")

    def set_tools(self, tools):
        """Set tools and create the agent"""
        self.tools = tools

        # Create the agent with tools using modern tool calling agent
        from langchain.agents import create_tool_calling_agent, AgentExecutor
        from langchain_core.prompts import ChatPromptTemplate

        # Create prompt template that will be dynamically updated with user context
        # Note: personalized_context will be injected dynamically during each conversation
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", MAIN_AGENT_PROMPT),  # Will be formatted with context during chat
            ("placeholder", "{chat_history}"),
            ("human", "{input}"),
            ("placeholder", "{agent_scratchpad}"),
        ])

        # Create tool calling agent (more modern than ReAct)
        agent = create_tool_calling_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=self.prompt_template
        )

        # Wrap in AgentExecutor for execution
        self.agent = AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            handle_parsing_errors=True,
            max_iterations=3
        )
        logger.info("✅ Agent created with tools")

    def _get_personalized_context(self) -> str:
        """Get personalized context for the user"""
        if not self.current_user or not hasattr(self, 'production_memory'):
            return "No user profile available."

        try:
            user_id = str(self.current_user.user.id)

            # Get production memory context
            if self.production_memory:
                production_context = self.production_memory.get_user_context(user_id)

                context = f"""USER CONTEXT:
{production_context}

IMPORTANT: This user has a permanent session. Use save_user_memory, search_user_memories, and update_user_profile tools to manage user information."""
                return context

            return "No user profile available."
        except Exception as e:
            logger.warning(f"Could not get personalized context: {e}")
            return "No user profile available."

    def _get_conversation_messages(self, thread_id: str) -> list:
        """Get conversation messages for memory system"""
        if not self.memory:
            return []

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if checkpoint:
                # Handle both dict and object checkpoint formats
                if isinstance(checkpoint, dict):
                    messages = checkpoint.get("channel_values", {}).get("messages", [])
                elif hasattr(checkpoint, 'channel_values'):
                    messages = checkpoint.channel_values.get("messages", [])
                else:
                    messages = []

                # Convert to memory system format, filtering out tool-related messages
                conversation_messages = []
                for msg in messages[-20:]:  # Last 20 messages for better context
                    if hasattr(msg, 'content') and msg.content:
                        # Skip tool messages and messages with tool calls for memory system
                        if hasattr(msg, 'type'):
                            if msg.type == 'tool':
                                continue  # Skip tool result messages
                            elif msg.type == 'ai' and hasattr(msg, 'tool_calls') and msg.tool_calls:
                                continue  # Skip AI messages with tool calls
                            elif msg.type == 'human':
                                role = 'user'
                            elif msg.type == 'ai':
                                role = 'assistant'
                            else:
                                role = 'user'  # Default to user
                        else:
                            role = 'user'  # Default to user

                        conversation_messages.append({
                            "role": role,
                            "content": msg.content
                        })

                return conversation_messages
            else:
                return []

        except Exception as e:
            logger.warning(f"Could not retrieve conversation messages: {e}")
            return []

    def _clean_conversation_history(self, thread_id: str) -> None:
        """Clean conversation history to remove incomplete tool calls"""
        if not self.memory:
            return

        try:
            config = {"configurable": {"thread_id": thread_id}}
            checkpoint = self.memory.get(config)

            if not checkpoint:
                return

            # Get messages from checkpoint
            if isinstance(checkpoint, dict):
                messages = checkpoint.get("channel_values", {}).get("messages", [])
            elif hasattr(checkpoint, 'channel_values'):
                messages = checkpoint.channel_values.get("messages", [])
            else:
                return

            # Check for incomplete tool calls
            tool_call_ids = set()
            tool_result_ids = set()

            # First pass: collect all tool call IDs and tool result IDs
            for msg in messages:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        tool_call_ids.add(tool_call.get('id'))

                if hasattr(msg, 'tool_call_id'):
                    tool_result_ids.add(msg.tool_call_id)

            # Find tool calls without results
            incomplete_tool_calls = tool_call_ids - tool_result_ids

            if incomplete_tool_calls:
                logger.warning(f"Found {len(incomplete_tool_calls)} incomplete tool calls, cleaning history")
                # For now, we'll let LangGraph handle this naturally
                # The error suggests the issue is with the LLM provider validation
                # We could implement more sophisticated cleaning here if needed

        except Exception as e:
            logger.warning(f"Could not clean conversation history: {e}")

    def _get_tool_description(self, tool_name: str) -> str:
        """Get detailed description of what each tool does"""
        descriptions = {
            'search_products': 'Searched for courses, programs, and educational products',
            'search_information': 'Searched for general information and troubleshooting help',
            'handle_booking': 'Processed booking request and managed enrollment workflow'
        }
        return descriptions.get(tool_name, f'Used {tool_name}')

    def _add_booking_reminder_if_needed(self, response: str, user_message: str, thread_id: str) -> str:
        """Add booking reminder if user has pending booking and is asking about other topics"""
        # Only check for reminders if user is NOT currently talking about booking
        booking_keywords = ['book', 'booking', 'enroll', 'register', 'sign up', 'course enrollment']
        is_booking_related = any(keyword in user_message.lower() for keyword in booking_keywords)

        if not is_booking_related and self.current_user:
            try:
                # Get booking agent from ChatService (we need to access it through current_user)
                # This is a bit of a hack, but we need access to the booking agent
                from api.services.chat_service import ChatService
                chat_service = ChatService(self.current_user)
                booking_agent = chat_service.booking_agent

                reminder = booking_agent.get_pending_booking_reminder(thread_id)
                if reminder:
                    response += reminder
            except Exception as e:
                logger.warning(f"Could not check for pending bookings: {e}")

        return response

    def chat(self, message: str, thread_id: str = None) -> dict:
        """
        Process a user message and return the response using LangGraph

        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management (uses permanent session if None)

        Returns:
            Dict with response and tools_used
        """
        user_id = str(self.current_user.user.id)

        # Use permanent session if no thread_id provided
        if thread_id is None:
            if hasattr(self, 'production_memory') and self.production_memory:
                session = self.production_memory.get_permanent_session(user_id)
                thread_id = session.thread_id
            else:
                thread_id = f"permanent_{user_id}"
        if not self.agent:
            raise ValueError("Agent not initialized. Call set_tools() first.")

        # Validate message content
        if not message or not message.strip():
            return {
                "response": "I didn't receive any message. Please tell me how I can help you today!",
                "tools_used": []
            }

        # Limit message length to prevent API issues
        if len(message) > 2000:
            return {
                "response": "Your message is too long. Please keep it under 2000 characters and try again.",
                "tools_used": []
            }

        log_user_input(message)

        # Store message in chat history
        if hasattr(self, 'production_memory') and self.production_memory:
            chat_history = self.production_memory.get_chat_history(user_id)
            chat_history.add_user_message(message)

        # Configure thread for memory persistence with user_id for memory tools
        config = {
            "configurable": {
                "thread_id": thread_id,
                "user_id": user_id,
                "checkpoint_ns": ""  # Add checkpoint namespace for MongoDB saver
            }
        }

        try:
            # Debug: Check if there's existing conversation history
            try:
                existing_checkpoint = self.memory.get(config)
                if existing_checkpoint:
                    logger.info(f"🔍 Found existing conversation history for thread {thread_id}")
                    # Log the number of messages in history
                    if hasattr(existing_checkpoint, 'channel_values'):
                        messages = existing_checkpoint.channel_values.get("messages", [])
                        logger.info(f"📝 Previous conversation has {len(messages)} messages")
                    else:
                        logger.info(f"📝 Found checkpoint data: {type(existing_checkpoint)}")
                else:
                    logger.info(f"🆕 Starting new conversation for thread {thread_id}")
            except Exception as e:
                logger.warning(f"⚠️ Could not check conversation history: {e}")

            # Production memory system handles user context automatically through tools

            # Get chat history from MongoDB memory for AgentExecutor
            chat_history = []
            if self.memory:
                try:
                    existing_checkpoint = self.memory.get(config)
                    if existing_checkpoint and hasattr(existing_checkpoint, 'channel_values'):
                        messages = existing_checkpoint.channel_values.get('messages', [])
                        # Convert to chat history format for AgentExecutor
                        chat_history = messages
                except Exception as e:
                    logger.warning(f"Could not retrieve chat history: {e}")

            # Get fresh user context for this conversation
            current_user_context = self._get_personalized_context()

            # Create dynamic prompt with current user context
            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", MAIN_AGENT_PROMPT.format(personalized_context=current_user_context)),
                ("placeholder", "{chat_history}"),
                ("human", "{input}"),
                ("placeholder", "{agent_scratchpad}"),
            ])

            # Update agent with fresh context
            from langchain.agents import create_tool_calling_agent, AgentExecutor
            fresh_agent = create_tool_calling_agent(self.llm, self.tools, dynamic_prompt)
            fresh_executor = AgentExecutor(
                agent=fresh_agent,
                tools=self.tools,
                verbose=False,
                handle_parsing_errors=True,
                max_iterations=3
            )

            # Invoke the agent with the message using AgentExecutor format
            try:
                response = fresh_executor.invoke({
                    "input": message,
                    "chat_history": chat_history
                })
            except Exception as e:
                # Handle tool call history issues
                if "tool_calls that do not have a corresponding ToolMessage" in str(e):
                    logger.warning("⚠️ Tool call history issue detected, clearing conversation history")
                    # Clear the conversation history for this thread to start fresh
                    try:
                        # Clear from MongoDB memory
                        if hasattr(self.current_user, 'db'):
                            checkpoints_collection = self.current_user.db["checkpoints"]
                            checkpoint_writes_collection = self.current_user.db["checkpoint_writes"]

                            checkpoints_collection.delete_many({"thread_id": thread_id})
                            checkpoint_writes_collection.delete_many({"thread_id": thread_id})

                            logger.info("✅ Cleared conversation history, retrying...")

                            # Retry the request with clean history using fresh executor
                            response = fresh_executor.invoke({
                                "input": message,
                                "chat_history": []
                            })
                        else:
                            raise e
                    except Exception as retry_error:
                        logger.error(f"Failed to recover from tool call issue: {retry_error}")
                        raise e
                else:
                    raise e

            # Extract the final response from AgentExecutor
            final_response = response.get("output", "I apologize, but I couldn't process your request properly.")

            # Store AI response in chat history
            if hasattr(self, 'production_memory') and self.production_memory:
                chat_history = self.production_memory.get_chat_history(user_id)
                chat_history.add_ai_message(final_response)

            # Memory persistence is handled automatically by AgentExecutor with MongoDB checkpointer

            # Check for pending bookings and add reminder if needed
            final_response = self._add_booking_reminder_if_needed(final_response, message, thread_id)

            log_agent_response(final_response)

            # Extract tool usage information from AgentExecutor intermediate steps
            tools_used = []
            if "intermediate_steps" in response:
                for step in response["intermediate_steps"]:
                    if len(step) >= 2:
                        action, observation = step[0], step[1]
                        tool_name = getattr(action, 'tool', 'unknown')
                        tool_input = getattr(action, 'tool_input', {})

                        # Truncate long outputs for display
                        tool_output = str(observation)
                        if len(tool_output) > 200:
                            tool_output = tool_output[:200] + "..."

                        tools_used.append({
                            'name': tool_name,
                            'description': self._get_tool_description(tool_name),
                            'input': tool_input,
                            'output': tool_output
                        })

            # Log the complete chat exchange with clean formatting
            log_chat_exchange(message, tools_used, final_response)

            return {
                "response": final_response,
                "tools_used": tools_used
            }
            
        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)

            # Provide user-friendly error messages
            if "contents is not specified" in str(e) or "empty content" in str(e):
                user_response = "I didn't receive a valid message. Please tell me how I can help you today!"
            elif "400" in str(e) and "Gemini" in str(e):
                user_response = "I'm having trouble processing your request. Please try rephrasing your message."
            elif "timeout" in str(e).lower():
                user_response = "The request is taking too long. Please try again with a shorter message."
            else:
                user_response = "I'm experiencing technical difficulties. Please try again in a moment."

            return {
                "response": user_response,
                "tools_used": []
            }

    async def chat_stream(self, message: str, thread_id: str = None):
        """
        Process a user message and stream the response using LangChain streaming

        Args:
            message: User's message
            thread_id: Conversation thread ID for memory management (uses permanent session if None)

        Yields:
            Dict chunks with streaming response and tools_used
        """
        user_id = str(self.current_user.user.id)

        # Use permanent session if no thread_id provided
        if thread_id is None:
            if hasattr(self, 'production_memory') and self.production_memory:
                session = self.production_memory.get_permanent_session(user_id)
                thread_id = session.thread_id
            else:
                thread_id = f"permanent_{user_id}"

        if not self.agent:
            raise ValueError("Agent not initialized. Call set_tools() first.")

        # Validate message content
        if not message or not message.strip():
            yield {
                "type": "complete",
                "response": "I didn't receive any message. Please tell me how I can help you today!",
                "tools_used": []
            }
            return

        # Limit message length to prevent API issues
        if len(message) > 2000:
            yield {
                "type": "complete",
                "response": "Your message is too long. Please keep it under 2000 characters and try again.",
                "tools_used": []
            }
            return

        log_user_input(message)

        # Store message in chat history
        if hasattr(self, 'production_memory') and self.production_memory:
            chat_history = self.production_memory.get_chat_history(user_id)
            chat_history.add_user_message(message)

        # Configure thread for memory persistence with user_id for memory tools
        config = {
            "configurable": {
                "thread_id": thread_id,
                "user_id": user_id,
                "checkpoint_ns": ""  # Add checkpoint namespace for MongoDB saver
            }
        }

        try:
            # Get chat history from MongoDB memory for AgentExecutor
            chat_history = []
            if self.memory:
                try:
                    existing_checkpoint = self.memory.get(config)
                    if existing_checkpoint and hasattr(existing_checkpoint, 'channel_values'):
                        messages = existing_checkpoint.channel_values.get('messages', [])
                        # Convert to chat history format for AgentExecutor
                        chat_history = messages
                except Exception as e:
                    logger.warning(f"Could not retrieve chat history: {e}")

            # Get fresh user context for streaming
            current_user_context = self._get_personalized_context()

            # Create dynamic prompt with current user context for streaming
            dynamic_prompt = ChatPromptTemplate.from_messages([
                ("system", MAIN_AGENT_PROMPT.format(personalized_context=current_user_context)),
                ("placeholder", "{chat_history}"),
                ("human", "{input}"),
                ("placeholder", "{agent_scratchpad}"),
            ])

            # Create fresh executor for streaming with current context
            from langchain.agents import create_tool_calling_agent, AgentExecutor
            fresh_agent = create_tool_calling_agent(self.llm, self.tools, dynamic_prompt)
            fresh_executor = AgentExecutor(
                agent=fresh_agent,
                tools=self.tools,
                verbose=False,
                handle_parsing_errors=True,
                max_iterations=3
            )

            # Stream the agent response using astream
            full_response = ""
            tools_used = []

            try:
                async for chunk in fresh_executor.astream({
                    "input": message,
                    "chat_history": chat_history
                }, config=config):

                    # Handle different chunk types from AgentExecutor
                    if "agent" in chunk:
                        # Agent is thinking/planning
                        agent_chunk = chunk["agent"]
                        if hasattr(agent_chunk, 'content') and agent_chunk.content:
                            # Stream the agent's response content
                            yield {
                                "type": "chunk",
                                "content": agent_chunk.content,
                                "full_response": full_response + agent_chunk.content
                            }
                            full_response += agent_chunk.content

                    elif "tools" in chunk:
                        # Tool execution
                        tool_chunk = chunk["tools"]
                        if isinstance(tool_chunk, list):
                            for tool_result in tool_chunk:
                                if hasattr(tool_result, 'name'):
                                    tool_name = tool_result.name
                                    tool_output = str(tool_result.content) if hasattr(tool_result, 'content') else str(tool_result)

                                    # Truncate long outputs for display
                                    if len(tool_output) > 200:
                                        tool_output = tool_output[:200] + "..."

                                    tools_used.append({
                                        'name': tool_name,
                                        'description': self._get_tool_description(tool_name),
                                        'input': {},
                                        'output': tool_output
                                    })

                                    # Yield tool usage info
                                    yield {
                                        "type": "tool",
                                        "tool_name": tool_name,
                                        "tool_output": tool_output
                                    }

                    elif "output" in chunk:
                        # Final output from agent
                        output_chunk = chunk["output"]
                        if isinstance(output_chunk, str):
                            # Stream the final response
                            yield {
                                "type": "chunk",
                                "content": output_chunk,
                                "full_response": output_chunk
                            }
                            full_response = output_chunk
                        elif isinstance(output_chunk, dict) and "output" in output_chunk:
                            final_output = output_chunk["output"]
                            yield {
                                "type": "chunk",
                                "content": final_output,
                                "full_response": final_output
                            }
                            full_response = final_output

            except Exception as e:
                # Handle tool call history issues
                if "tool_calls that do not have a corresponding ToolMessage" in str(e):
                    logger.warning("⚠️ Tool call history issue detected, clearing conversation history")
                    # Clear the conversation history for this thread to start fresh
                    try:
                        # Clear from MongoDB memory
                        if hasattr(self.current_user, 'db'):
                            checkpoints_collection = self.current_user.db["checkpoints"]
                            checkpoint_writes_collection = self.current_user.db["checkpoint_writes"]

                            checkpoints_collection.delete_many({"thread_id": thread_id})
                            checkpoint_writes_collection.delete_many({"thread_id": thread_id})

                            logger.info("✅ Cleared conversation history, retrying...")

                            # Retry the request with clean history
                            async for chunk in self.agent.astream({
                                "input": message,
                                "chat_history": []
                            }, config=config):

                                if "agent" in chunk:
                                    agent_chunk = chunk["agent"]
                                    if hasattr(agent_chunk, 'content') and agent_chunk.content:
                                        yield {
                                            "type": "chunk",
                                            "content": agent_chunk.content,
                                            "full_response": full_response + agent_chunk.content
                                        }
                                        full_response += agent_chunk.content

                                elif "output" in chunk:
                                    output_chunk = chunk["output"]
                                    if isinstance(output_chunk, str):
                                        yield {
                                            "type": "chunk",
                                            "content": output_chunk,
                                            "full_response": output_chunk
                                        }
                                        full_response = output_chunk
                        else:
                            raise e
                    except Exception as retry_error:
                        logger.error(f"Failed to recover from tool call issue: {retry_error}")
                        raise e
                else:
                    raise e

            # Store AI response in chat history
            if hasattr(self, 'production_memory') and self.production_memory:
                chat_history = self.production_memory.get_chat_history(user_id)
                chat_history.add_ai_message(full_response)

            # Memory persistence is handled automatically by AgentExecutor with MongoDB checkpointer

            # Check for pending bookings and add reminder if needed
            final_response = self._add_booking_reminder_if_needed(full_response, message, thread_id)
            if final_response != full_response:
                # Stream the booking reminder
                reminder_text = final_response[len(full_response):]
                yield {
                    "type": "chunk",
                    "content": reminder_text,
                    "full_response": final_response
                }
                full_response = final_response

            log_agent_response(full_response)

            # Send completion signal
            yield {
                "type": "complete",
                "response": full_response,
                "tools_used": tools_used
            }

        except Exception as e:
            error_msg = f"Error processing request: {str(e)}"
            logger.error(error_msg)

            # Provide user-friendly error messages
            if "contents is not specified" in str(e) or "empty content" in str(e):
                user_response = "I didn't receive a valid message. Please tell me how I can help you today!"
            elif "400" in str(e) and "Gemini" in str(e):
                user_response = "I'm having trouble processing your request. Please try rephrasing your message."
            elif "timeout" in str(e).lower():
                user_response = "The request is taking too long. Please try again with a shorter message."
            else:
                user_response = "I'm experiencing technical difficulties. Please try again in a moment."

            yield {
                "type": "error",
                "response": user_response,
                "tools_used": []
            }
